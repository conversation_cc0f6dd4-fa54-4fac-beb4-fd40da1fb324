#' Simple Test Runner
#' 
#' This script runs tests without requiring package structure
#' 
#' <AUTHOR> Team
#' @version 2.0.0

cat("Running Simple Test Suite\n")
cat("=========================\n\n")

# Check if testthat is available
if (!requireNamespace("testthat", quietly = TRUE)) {
  cat("❌ testthat package not available. Installing...\n")
  install.packages("testthat")
}

library(testthat)

# Set working directory to project root
if (basename(getwd()) != "scoring-candidate") {
  if (file.exists("api.R")) {
    # Already in project root
  } else {
    stop("Please run this script from the project root directory")
  }
}

# Set test environment
Sys.setenv(
  APP_ENV = "test",
  DB_NAME_READ = "test_db",
  DB_HOST_READ = "localhost",
  DB_PORT_READ = "5432",
  DB_USER_READ = "test_user",
  DB_PASSWORD_READ = "test_password",
  DB_NAME_WRITE = "test_db",
  DB_HOST_WRITE = "localhost",
  DB_PORT_WRITE = "5432",
  DB_USER_WRITE = "test_user",
  DB_PASSWORD_WRITE = "test_password",
  AWS_ACCESS_KEY_ID = "test_key",
  AWS_SECRET_ACCESS_KEY = "test_secret",
  AWS_DEFAULT_REGION = "us-east-1",
  VALID_TOKEN = "test_token_123"
)

# Function to run individual test files
run_test_file <- function(test_file) {
  cat("Running", test_file, "...\n")
  tryCatch({
    test_results <- test_file(test_file, reporter = "summary")
    cat("✅", test_file, "completed\n\n")
    return(TRUE)
  }, error = function(e) {
    cat("❌", test_file, "failed:", e$message, "\n\n")
    return(FALSE)
  })
}

# Run tests
test_files <- c(
  "tests/testthat/test-scoring-algorithms.R",
  "tests/testthat/test-input-validators.R"
)

results <- list()
for (test_file in test_files) {
  if (file.exists(test_file)) {
    results[[test_file]] <- run_test_file(test_file)
  } else {
    cat("⚠️", test_file, "not found\n")
    results[[test_file]] <- FALSE
  }
}

# Summary
total_tests <- length(results)
passed_tests <- sum(unlist(results))

cat("=========================\n")
cat("Test Summary:\n")
cat("Total test files:", total_tests, "\n")
cat("Passed:", passed_tests, "\n")
cat("Failed:", total_tests - passed_tests, "\n")

if (passed_tests == total_tests) {
  cat("🎉 All tests passed!\n")
} else {
  cat("⚠️ Some tests failed. Please check the output above.\n")
}

cat("=========================\n")
